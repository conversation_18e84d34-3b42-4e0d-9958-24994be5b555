<?php
/* Smarty version 3.1.29, created on 2025-07-14 00:05:56
  from "C:\xampp\htdocs\RajaGenWeb\templates\js\page\notification_js.tpl" */

if ($_smarty_tpl->smarty->ext->_validateCompiled->decodeProperties($_smarty_tpl, array (
  'has_nocache_code' => false,
  'version' => '3.1.29',
  'unifunc' => 'content_68741fb4869513_31475997',
  'file_dependency' => 
  array (
    '894ec4e16adf31d73e4181872ec906579c02c9b6' => 
    array (
      0 => 'C:\\xampp\\htdocs\\RajaGenWeb\\templates\\js\\page\\notification_js.tpl',
      1 => 1741775697,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_68741fb4869513_31475997 ($_smarty_tpl) {
echo '<script'; ?>
>
$('document').ready(function()
{
    function normal_modalize(title, body) {
		$(".normal-modalize").modal({
			backdrop: "static"
		});
		$(".normal-modal-title").html(title);
		$(".normal-modal-html").html(body);
	}
	
    function getNotification(){
        $.ajax({
            url: "<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
serverside/data/get_notification.php",
            type: "GET",
            dataType: "JSON",
    		cache: false,
            success: function(data)
            {
                var notifications = '';
    			if(data.response == 1)
    			{
    			    if (data.notiftotal === 0) {
    			        $(".notification-toggle").removeClass("beep");
    				    notifications = `<a href="javascript:void(0)" class="dropdown-item dropdown-item-unread">
                                        	<div class="dropdown-item-icon bg-primary text-white">
                                        		<i class="far fa-smile"></i>
                                        	</div>
                                        	<div class="dropdown-item-desc"> No available notifications to show for now. <div class="time text-primary">Stay safe and secure.</div>
                                        	</div>
                                        </a>`;
    			    }else{
    			        $(".notification-toggle").addClass("beep");
    			        notifications = data.notifica;
    			    }
    			    $(".profile-notifications").html(notifications);
    			}
    			if(data.response == 2)
    			{
    				swal(`Failed`, `Failed getting data from AJAX.`, `warning`, {
                        button: false,
                        closeOnClickOutside: false,
                        timer: 3000
                    }).then(() => {
                        location.reload()
                    });
    			}
    			if(data.response == 0){
    				swal(`Failed`, `Failed getting data from AJAX.`, `warning`, {
                        button: false,
                        closeOnClickOutside: false,
                        timer: 3000
                    }).then(() => {
                        location.reload()
                    });
    			}
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                swal(`Failed`, `Failed getting data from AJAX.`, `warning`, {
                    button: false,
                    closeOnClickOutside: false,
                    timer: 3000
                }).then(() => {
                    location.reload()
                });
            }
        });
    }
    
    $(".notificationlist").on("click", ".view-notification", function() {
        var id = $(this).data("id");
        var type = $(this).data("type");
        var date = $(this).data("date");
    
        $.ajax({
            url: "<?php echo $_smarty_tpl->tpl_vars['base_url']->value;?>
serverside/data/read_notification.php",
            data: "id="+id,
            type: "GET",
            dataType: "JSON",
    		cache: false,
            success: function(data)
            {
    			if(data.response == 1){
            		normal_modalize(`<span class="badge badge-primary">`+type+`</span> <span class="text-small text-primary">Posted `+date+` </span>`, data.content);
            	}
            	if(data.response == 2){
            		modalMessage('danger','Error', data.msg);
            	}
            	if(data.response == 3){
            		modalMessage('danger','Error', data.errormsg);
            	}
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                swal(`Failed`, `Failed getting data from AJAX.`, `warning`, {
                    button: false,
                    closeOnClickOutside: false,
                    timer: 3000
                }).then(() => {
                    location.reload()
                });
            },
            complete: function(){
    
    		}
        });
        
    })
    
getNotification()
    setInterval(function () {
        getNotification()
    }, 5000);
});
<?php echo '</script'; ?>
><?php }
}
